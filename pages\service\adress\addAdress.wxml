<!--pages/service/adress/addAdress.wxml-->
<view class="container containeraddadress">
  <view class="adresstip">— 请完善您的服务地址—</view>
  <form bindsubmit="submitForm" class="flex diygw-form flex-direction-column diygw-col-24 adressform">
    <input hidden type="hidden" name="id" value="{{form.id}}" />
    <input hidden type="hidden" name="customerId" value="{{form.customerId}}" />
    <view class="diygw-form-item diygw-col-24 noborder">
      <view class="title"><text class="text-red">* </text>联系人 </view>
      <view class="input">
        <input class="flex1" name="contactName" comfirm-type="done" type="text" value="{{form.contactName}}" data-key="form.contactName" bindchange="changeValue" placeholder="请输入联系人" />
      </view>
    </view>
    <view class="diygw-form-item diygw-col-24 noborder">
      <view class="title"><text class="text-red">* </text>手机号码 </view>
      <view class="input">
        <input class="flex1" name="contactPhone" comfirm-type="done" type="text" value="{{form.contactPhone}}" data-key="form.contactPhone" bindchange="changeValue" placeholder="请输入手机号码" />
      </view>
    </view>
    <view class="diygw-form-item diygw-col-24 noborder">
      <view class="title"><text class="text-red">* </text>所在地址 </view>
      <view class="input">
        <input class="flex1" name="addressText" comfirm-type="done" type="text" value="{{form.addressText}}" data-key="form.addressText" bindchange="changeValue" placeholder="请填写详细地址" />
      </view>
      <input hidden type="hidden" name="addressArea" value="{{form.addressArea}}" />
      <input hidden type="hidden" name="addressCode" value="{{form.addressCode}}" />
      <input hidden type="hidden" name="longitude" value="{{form.longitude}}" />
      <input hidden type="hidden" name="latitude" value="{{form.latitude}}" />
    </view>

    <!-- 地图选择位置按钮 -->
    <view class="diygw-form-item diygw-col-24 noborder">
      <view class="map-location-btn" bind:tap="getLocation">
        <view class="location-icon">
          <text class="icon6 diy-icon-locationfill"></text>
        </view>
        <view class="location-content">
          <view class="location-title">在地图上选择位置</view>
          <view class="location-desc">点击选择精确位置，自动填充地址信息</view>
        </view>
        <view class="location-arrow">
          <text class="icon6 diy-icon-right"></text>
        </view>
      </view>
    </view>
    <view class="diygw-form-item diygw-col-24 noborder">
      <view class="title"> 最近出入口 </view>
      <view class="input">
        <input class="flex1" name="remark" comfirm-type="done" type="text" value="{{form.remark}}" data-key="form.remark" bindchange="changeValue" placeholder="如：小区东门" />
      </view>
    </view>
    <view class="diygw-form-item diygw-col-24 noborder">
      <view class="title title-mb5 width-auto"> 设为默认地址 </view>
      <view class="input">
        <view data-checked="{{form.isDefault}}" catchtap="changeFormSwitched">
          <input hidden name="isDefault" type="hidden" value="{{form.isDefault?1:0}}" />
          <view class="diygw-switch-box {{form.isDefault?'checked':''}}"></view>
        </view>
      </view>
    </view>
    <view class="flex flex-wrap diygw-col-24 items-end diygw-bottom flex1-clz">
      <view class="flex flex-wrap diygw-col-24 flex-direction-column items-center flex12-clz">
        <button form-type="submit" class="diygw-col-24 bg-none btn-clz diygw-btn-default">{{isUse ?"保存并使用":"保存"}}</button>
      </view>
    </view>
  </form>
  <!-- 地址选择器弹窗 -->
  <region-picker show="{{showRegionPicker}}" bind:confirm="onAddressConfirm" bind:cancel="onAddressCancel" />
</view>