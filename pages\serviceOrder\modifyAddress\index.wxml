<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-content">
      <view class="back-btn" bindtap="goBack">
        <text class="icon-back">‹</text>
      </view>
      <text class="page-title">修改服务地址</text>
      <view class="placeholder"></view>
    </view>
  </view>

  <!-- 当前地址显示 -->
  <view class="current-address-section">
    <view class="section-title">当前服务地址</view>
    <view class="current-address">
      <text class="address-text">{{currentAddress}}</text>
      <text wx:if="{{currentAddressDetail}}" class="address-detail">{{currentAddressDetail}}</text>
    </view>
  </view>

  <!-- 地址修改表单 -->
  <view class="form-section">
    <view class="section-title">修改为新地址</view>
    
    <!-- 选择已保存地址 -->
    <view class="form-item">
      <view class="form-label">选择已保存地址</view>
      <view class="address-selector" bindtap="showAddressSelector">
        <text class="selector-text">{{selectedAddress ? selectedAddress.address : '点击选择地址'}}</text>
        <text class="arrow-right">></text>
      </view>
    </view>

    <!-- 或者手动输入 -->
    <view class="divider">
      <text class="divider-text">或手动输入</text>
    </view>

    <!-- 地址输入 -->
    <view class="form-item">
      <view class="form-label">服务地址 *</view>
      <input 
        class="form-input" 
        placeholder="请输入服务地址"
        value="{{form.address}}"
        bindinput="onAddressInput"
      />
    </view>

    <!-- 详细地址输入 -->
    <view class="form-item">
      <view class="form-label">详细地址 *</view>
      <input 
        class="form-input" 
        placeholder="如：xx小区xx栋xx室"
        value="{{form.addressDetail}}"
        bindinput="onAddressDetailInput"
      />
    </view>

    <!-- 地址备注 -->
    <view class="form-item">
      <view class="form-label">地址备注</view>
      <textarea 
        class="form-textarea" 
        placeholder="如：门口有保安，请提前联系"
        value="{{form.addressRemark}}"
        bindinput="onAddressRemarkInput"
        maxlength="200"
      ></textarea>
    </view>

    <!-- 地图选择位置按钮 -->
    <view class="map-location-btn" bindtap="chooseLocation">
      <view class="location-icon">
        <text class="icon-location">📍</text>
      </view>
      <view class="location-content">
        <view class="location-title">在地图上选择位置</view>
        <view class="location-desc">点击选择精确位置，自动填充地址信息</view>
      </view>
      <view class="location-arrow">
        <text class="arrow-right">></text>
      </view>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <button 
      class="submit-btn {{loading ? 'disabled' : ''}}" 
      bindtap="submitModify"
      disabled="{{loading}}"
    >
      {{loading ? '修改中...' : '确认修改'}}
    </button>
  </view>

  <!-- 地址选择弹窗 -->
  <view wx:if="{{showAddressList}}" class="address-modal">
    <view class="modal-mask" bindtap="hideAddressSelector"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">选择地址</text>
        <text class="modal-close" bindtap="hideAddressSelector">×</text>
      </view>
      <scroll-view class="address-list" scroll-y>
        <view 
          wx:for="{{addressList}}" 
          wx:key="id" 
          class="address-item"
          data-address="{{item}}"
          bindtap="selectAddress"
        >
          <view class="address-main">
            <text class="address-name">{{item.address}}</text>
            <text class="address-detail">{{item.addressDetail}}</text>
          </view>
          <view wx:if="{{item.isDefault}}" class="default-tag">默认</view>
        </view>
        <view wx:if="{{addressList.length === 0}}" class="empty-address">
          <text class="empty-text">暂无保存的地址</text>
        </view>
      </scroll-view>
    </view>
  </view>
</view>
