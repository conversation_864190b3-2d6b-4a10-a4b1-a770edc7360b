<view class="container">
  <view class="order-info">
    <view class="order-content" wx:if="{{orderDetail.orderDetails && orderDetail.orderDetails.length > 0}}">
      <image class="product-image" src="{{orderDetail.orderDetails[0].service.logo}}"></image>
      <view class="product-info">
        <view class="flex align-center justify-between">
          <text class="product-name">{{orderDetail.orderDetails[0].service.serviceName}}</text>
          <text class="product-price">¥ <text class="paid-money">{{orderDetail.totalFee}}</text></text>
        </view>
        <view class="flex align-center justify-between">
          <text wx:if="{{!!orderDetail.orderDetails[0].additionalServices.length}}" class="product-service">增项服务：<text wx:for="{{orderDetail.orderDetails[0].additionalServices}}" wx:for-item="val" wx:key="val.id">{{val.name}};</text></text>
          <text wx:else class="product-service">增项服务：无</text>
          <!-- <text class="product-quantity">x 1</text> -->
        </view>
      </view>
    </view>

    <view class="order-details">
      <view class="detail-item" wx:if="{{orderDetail.originalPrice}}">
        <text class="label">原价</text>
        <text class="total-price">¥ <text class="paid-money">{{orderDetail.originalPrice}}</text></text>
      </view>
      <view class="detail-item">
        <text class="label">服务总价</text>
        <text class="total-price">¥ <text class="paid-money">{{orderDetail.totalFee}}</text></text>
      </view>
      <view class="detail-item">
        <text class="label">实付款</text>
        <text class="total-price">¥ <text class="paid-money">{{orderDetail.totalFee}}</text></text>
      </view>
      <view class="detail-item">
        <text class="label">订单编号</text>
        <text class="content">{{orderDetail.sn}}</text>
      </view>
      <view wx:if="{{orderDetail.orderDetails && orderDetail.orderDetails.length > 0}}" class="detail-item">
        <text class="label">服务宠物</text>
        <text class="content">{{orderDetail.orderDetails[0].petName}}</text>
      </view>
      <view class="detail-item">
        <text class="label">服务人员</text>
        <text class="content">{{orderDetail.employee.name}}</text>
      </view>
      <view class="detail-item">
        <text class="label">期望上门时间</text>
        <text class="content">{{orderDetail.serviceTime || '待预约'}}</text>
      </view>
      <view class="detail-item">
        <text class="label">服务地址</text>
        <view class="address-content">
          <view wx:if="{{canModifyAddress}}" class="edit-btn" bindtap="modifyServiceAddress">
            <text class="edit-text">修改</text>
          </view>
          <text class="content">{{orderDetail.address}}</text>
        </view>
      </view>
      <view class="detail-item">
        <text class="label">下单时间</text>
        <text class="content">{{orderDetail.orderTime}}</text>
      </view>
      <view wx:if="{{orderDetail.orderDetails && orderDetail.orderDetails.length > 0 && orderDetail.orderDetails[0].userRemark}}" class="detail-item">
        <text class="label">用户备注</text>
        <text class="content">{{orderDetail.orderDetails[0].userRemark}}</text>
      </view>
      <view class="detail-item hidden">
        <text class="label">付款时间</text>
        <text class="content">{{orderDetail.payAt}}</text>
      </view>
      <view class="detail-item hidden">
        <text class="label">服务时间</text>
        <text class="content">{{orderDetail.serviceAt}}</text>
      </view>
      <view class="detail-item hidden">
        <text class="label">完成时间</text>
        <text class="content">{{orderDetail.dealAt}}</text>
      </view>
    </view>
  </view>

  <!-- 服务照片区域 -->
  <view wx:if="{{servicePhotos && ((servicePhotos.beforePhotos && servicePhotos.beforePhotos.length > 0) || (servicePhotos.afterPhotos && servicePhotos.afterPhotos.length > 0))}}" class="service-photos">
    <!-- 服务前照片 -->
    <view wx:if="{{servicePhotos.beforePhotos && servicePhotos.beforePhotos.length > 0}}" class="photo-section">
      <view class="section-title">
        <text class="title-text">服务前照片</text>
        <text class="photo-time" wx:if="{{servicePhotos.beforePhotoTime}}">{{servicePhotos.beforePhotoTime}}</text>
      </view>
      <view class="photo-grid">
        <view wx:for="{{servicePhotos.beforePhotos}}" wx:key="index" class="photo-item">
          <image
            src="{{item}}"
            class="photo-preview"
            mode="aspectFill"
            bindtap="previewServicePhoto"
            data-url="{{item}}"
            data-type="before"
          ></image>
        </view>
      </view>
    </view>

    <!-- 服务后照片 -->
    <view wx:if="{{servicePhotos.afterPhotos && servicePhotos.afterPhotos.length > 0}}" class="photo-section">
      <view class="section-title">
        <text class="title-text">服务后照片</text>
        <text class="photo-time" wx:if="{{servicePhotos.afterPhotoTime}}">{{servicePhotos.afterPhotoTime}}</text>
      </view>
      <view class="photo-grid">
        <view wx:for="{{servicePhotos.afterPhotos}}" wx:key="index" class="photo-item">
          <image
            src="{{item}}"
            class="photo-preview"
            mode="aspectFill"
            bindtap="previewServicePhoto"
            data-url="{{item}}"
            data-type="after"
          ></image>
        </view>
      </view>
    </view>

    <!-- 照片墙入口 -->
    <view wx:if="{{photoWallData}}" class="photo-wall-entry-section">
      <view class="photo-wall-card" bind:tap="viewPhotoWallDetail">
        <view class="photo-wall-header">
          <view class="photo-wall-icon">📸</view>
          <view class="photo-wall-info">
            <text class="photo-wall-title">查看照片墙</text>
            <text class="photo-wall-desc">精彩服务瞬间已上传照片墙</text>
          </view>
          <text class="arrow-right">></text>
        </view>
        <view class="photo-wall-stats">
          <view class="stat-item">
            <text class="stat-icon">👁</text>
            <text class="stat-text">{{photoWallData.viewCount || 0}} 浏览</text>
          </view>
          <view class="stat-item">
            <text class="stat-icon">♥</text>
            <text class="stat-text">{{photoWallData.likeCount || 0}} 点赞</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 追加服务卡片 -->
  <view wx:if="{{orderDetail.status === '服务中' && additionalServices.length > 0}}" class="additional-services-section">
    <view class="section-title">追加服务</view>
    <view class="additional-services-list">
      <view wx:for="{{additionalServices}}" wx:key="id" class="additional-service-card">
        <!-- 服务头部信息 -->
        <view class="service-header">
          <view class="service-info">
            <text class="service-name">{{item.details[0].serviceName}}</text>
            <text class="service-time">{{item.createdAt}}</text>
          </view>
          <view class="service-status" style="color: {{item.statusInfo.color}}">
            {{item.statusInfo.text}}
          </view>
        </view>

        <!-- 服务价格信息 -->
        <view class="service-price">
          <view class="price-item">
            <text class="price-label">原价：</text>
            <text class="price-value">¥{{item.originalPrice}}</text>
          </view>
          <view class="price-item">
            <text class="price-label">实付：</text>
            <text class="price-value highlight">¥{{item.totalFee}}</text>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="service-actions">
          <view wx:for="{{item.statusInfo.actions}}" wx:for-item="action" wx:key="*this" class="action-btn-small">
            <view wx:if="{{action === 'view'}}"
                  class="btn-outline"
                  bindtap="viewAdditionalServiceDetail"
                  data-id="{{item.id}}">
              查看
            </view>
            <view wx:elif="{{action === 'pay'}}"
                  class="btn-primary"
                  bindtap="payAdditionalService"
                  data-id="{{item.id}}">
              付款
            </view>
            <view wx:elif="{{action === 'delete'}}"
                  class="btn-danger"
                  bindtap="deleteAdditionalService"
                  data-id="{{item.id}}"
                  data-name="{{item.details[0].serviceName}}">
              删除
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 更多操作弹窗 -->
  <view wx:if="{{showMoreActions}}" class="more-actions-dropdown">
    <view class="dropdown-item" bindtap="viewOrderDetail" data-order-id="{{item.orderId}}">
      更改服务地址
    </view>
    <view class="dropdown-item" bindtap="deleteOrder" data-order-id="{{item.orderId}}">
      更换服务人员
    </view>
    <view class="dropdown-item" bindtap="toggleOrderActions" data-order-id="{{item.orderId}}">
      取消订单
    </view>
  </view>
  <view class="diygw-col-24 diygw-bottom flex1-clz">
    <!-- 双按钮布局 -->
    <view class="dual-button-layout">
      <!-- 左侧联系按钮 -->
      <view wx:if="{{canCallEmployee}}" class="modern-action-btn contact-btn" bindtap="callEmployee">
        <image src="//xian7.zos.ctyun.cn/pet/static/dianhua.png" class="btn-icon"></image>
        <text class="btn-text">联系服务人员</text>
      </view>

      <!-- 右侧主要操作按钮 -->
      <block wx:if="{{orderDetail.status === '待付款'}}">
        <view class="modern-action-btn pay-btn" bindtap="payOrder" data-sn="{{orderDetail.sn}}">
          <image src="//xian7.zos.ctyun.cn/pet/static/money.png" class="btn-icon"></image>
          <text class="btn-text">去付款</text>
        </view>
      </block>

      <block wx:elif="{{orderDetail.status === 'paid'}}">
        <view class="modern-action-btn urge-btn" bindtap="confirmReceipt" data-order-id="{{orderDetail.orderId}}">
          <image src="//xian7.zos.ctyun.cn/pet/static/shijian1.png" class="btn-icon"></image>
          <text class="btn-text">催接单</text>
        </view>
      </block>

      <block wx:elif="{{orderDetail.status === '服务中' && orderDetail.orderDetails && orderDetail.orderDetails.length > 0}}">
        <view class="modern-action-btn additional-service-btn" bindtap="applyAdditionalService" data-order-detail-id="{{orderDetail.orderDetails[0].id}}">
          <image src="//xian7.zos.ctyun.cn/pet/static/roundAdd.png" class="btn-icon"></image>
          <text class="btn-text">申请追加服务</text>
        </view>
      </block>

      <block wx:elif="{{orderDetail.status === 'completed'}}">
        <view class="modern-action-btn review-btn" bindtap="reviewOrder" data-order-id="{{orderDetail.orderId}}">
          <image src="//xian7.zos.ctyun.cn/pet/static/qt.png" class="btn-icon"></image>
          <text class="btn-text">去评价</text>
        </view>
      </block>
    </view>
  </view>
  <!-- 地址修改弹窗 -->
  <view wx:if="{{showAddressModal}}" class="address-modify-modal">
    <view class="modal-mask" bindtap="closeAddressModal"></view>
    <view class="modal-content">
      <!-- 弹窗头部 -->
      <view class="modal-header">
        <text class="modal-title">修改服务地址</text>
        <text class="modal-close" bindtap="closeAddressModal">×</text>
      </view>

      <!-- 选择位置方式 -->
      <view class="address-type-selector">
        <view class="section-label">选择位置</view>
        <view class="type-buttons">
          <view
            class="type-btn {{selectedAddressType === 'saved' ? 'active' : ''}}"
            data-type="saved"
            bindtap="selectAddressType"
          >
            获取已保存位置
          </view>
          <view
            class="type-btn {{selectedAddressType === 'manual' ? 'active' : ''}}"
            data-type="manual"
            bindtap="selectAddressType"
          >
            地图选择
          </view>
        </view>
      </view>

      <!-- 已保存地址列表 -->
      <view wx:if="{{selectedAddressType === 'saved'}}" class="saved-address-section">
        <scroll-view class="saved-address-list" scroll-y>
          <view
            wx:for="{{userAddressList}}"
            wx:key="id"
            class="address-card {{addressForm.addressId === item.id ? 'selected' : ''}}"
            data-address="{{item}}"
            bindtap="selectSavedAddress"
          >
            <!-- 地址主要信息 -->
            <view class="address-main-info">
              <text class="address-title">{{item.addressText}}</text>
              <text class="address-detail-text">{{item.detailAddress}}</text>
            </view>

            <!-- 地址标签和状态 -->
            <view class="address-tags">
              <view wx:if="{{item.isDefault}}" class="default-badge">默认</view>
              <view wx:if="{{item.longitude && item.latitude}}" class="coordinate-badge">有坐标</view>
              <view wx:if="{{addressForm.addressId === item.id}}" class="selected-badge">
                <text class="selected-icon">✓</text>
              </view>
            </view>
          </view>

          <view wx:if="{{userAddressList.length === 0}}" class="empty-address-state">
            <view class="empty-icon">📍</view>
            <text class="empty-text">暂无保存的地址</text>
            <text class="empty-tip">请先在地址管理中添加地址</text>
          </view>
        </scroll-view>
      </view>

      <!-- 地图选择按钮 -->
      <view wx:if="{{selectedAddressType === 'manual'}}" class="map-select-section">
        <view class="map-select-card" bindtap="chooseMapLocation">
          <view class="map-select-content">
            <view class="map-icon-wrapper">
              <text class="map-icon">📍</text>
            </view>
            <view class="map-text-wrapper">
              <text class="map-title">选择地图位置</text>
              <text class="map-subtitle">点击打开地图选择具体位置</text>
            </view>
          </view>
          <view class="map-arrow">></view>
        </view>
      </view>

      <!-- 服务地址输入 -->
      <view class="form-section">
        <view class="form-item">
          <view class="form-label">服务地址</view>
          <input
            class="form-input"
            placeholder="陕西省西安市雁塔区潘家庄街道大寨路39号"
            value="{{addressForm.address}}"
            bindinput="onAddressInput"
          />
        </view>

        <view class="form-item">
          <view class="form-label">详细地址 *</view>
          <input
            class="form-input"
            placeholder="陕西省西安市雁塔区潘家庄街道大寨路39号"
            value="{{addressForm.addressDetail}}"
            bindinput="onAddressDetailInput"
            maxlength="255"
          />
          <view class="char-count">{{addressForm.addressDetail.length}}/255</view>
        </view>

        <view class="form-item">
          <view class="form-label">地址备注</view>
          <textarea
            class="form-textarea"
            placeholder="小区东门"
            value="{{addressForm.addressRemark}}"
            bindinput="onAddressRemarkInput"
            maxlength="255"
          ></textarea>
          <view class="char-count">{{addressForm.addressRemark.length}}/255</view>
        </view>

        <!-- 经纬度调试信息（可选显示） -->
        <view wx:if="{{addressForm.longitude || addressForm.latitude}}" class="coordinate-info">
          <view class="coordinate-label">位置坐标</view>
          <view class="coordinate-text">
            经度: {{addressForm.longitude || '未设置'}} | 纬度: {{addressForm.latitude || '未设置'}}
          </view>
        </view>
      </view>

      <!-- 底部按钮 -->
      <view class="modal-footer">
        <button class="cancel-btn" bindtap="closeAddressModal">取消</button>
        <button
          class="confirm-btn {{addressModifying ? 'loading' : ''}}"
          bindtap="confirmModifyAddress"
          disabled="{{addressModifying}}"
        >
          {{addressModifying ? '修改中...' : '确认修改'}}
        </button>
      </view>
    </view>
  </view>

  <custom-modal
    show="{{showModal}}"
    title="{{modalTitle}}"
    content="{{modalContent}}"
    buttons="{{modalButtons}}"
    bind:confirm="handleModalConfirm"
    bind:cancel="handleModalCancel"
    bind:modalConfirm="handleModalConfirm"
    bind:modalCancel="handleModalCancel"
    bind:handlePayConfirm="handlePayConfirm"
    bind:handlePayModalCancel="handlePayModalCancel"
    bind:handleAdditionalServicePaymentSuccess="handleAdditionalServicePaymentSuccess"
  ></custom-modal>
</view>