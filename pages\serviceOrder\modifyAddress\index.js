import orderApi from '../../../api/modules/order.js';
import addressApi from '../../../api/modules/address.js';
import Session from '../../../common/Session.js';
import { OrderStatus } from '../../../common/constant.js';

Page({
  data: {
    userInfo: null,
    orderId: null,
    currentAddress: '',
    currentAddressDetail: '',
    addressList: [],
    selectedAddress: null,
    showAddressList: false,
    form: {
      address: '',
      addressDetail: '',
      longitude: null,
      latitude: null,
      addressRemark: '',
      addressId: null
    },
    loading: false
  },

  onLoad(options) {
    const { orderId, currentAddress, currentAddressDetail } = options;
    const userInfo = Session.get();
    
    this.setData({
      userInfo,
      orderId: parseInt(orderId),
      currentAddress: decodeURIComponent(currentAddress || ''),
      currentAddressDetail: decodeURIComponent(currentAddressDetail || ''),
      'form.address': decodeURIComponent(currentAddress || ''),
      'form.addressDetail': decodeURIComponent(currentAddressDetail || '')
    });

    // 加载用户地址列表
    this.loadAddressList();
  },

  /**
   * 加载用户地址列表
   */
  async loadAddressList() {
    try {
      const { userInfo } = this.data;
      if (!userInfo) return;

      const addressList = await addressApi.list(userInfo.id);
      this.setData({ addressList: addressList || [] });
    } catch (error) {
      console.error('加载地址列表失败:', error);
    }
  },

  /**
   * 显示地址选择列表
   */
  showAddressSelector() {
    this.setData({ showAddressList: true });
  },

  /**
   * 隐藏地址选择列表
   */
  hideAddressSelector() {
    this.setData({ showAddressList: false });
  },

  /**
   * 选择地址
   */
  selectAddress(e) {
    const { address } = e.currentTarget.dataset;
    this.setData({
      selectedAddress: address,
      'form.address': address.address,
      'form.addressDetail': address.addressDetail,
      'form.longitude': address.longitude,
      'form.latitude': address.latitude,
      'form.addressRemark': address.addressRemark || '',
      'form.addressId': address.id,
      showAddressList: false
    });
  },

  /**
   * 手动输入地址
   */
  onAddressInput(e) {
    this.setData({
      'form.address': e.detail.value,
      selectedAddress: null,
      'form.addressId': null
    });
  },

  /**
   * 手动输入地址详情
   */
  onAddressDetailInput(e) {
    this.setData({
      'form.addressDetail': e.detail.value
    });
  },

  /**
   * 输入地址备注
   */
  onAddressRemarkInput(e) {
    this.setData({
      'form.addressRemark': e.detail.value
    });
  },

  /**
   * 选择位置
   */
  async chooseLocation() {
    try {
      wx.showLoading({
        title: '打开地图中...'
      });

      const location = await wx.chooseLocation();
      wx.hideLoading();

      if (location) {
        // 先设置基本的位置信息
        this.setData({
          'form.address': location.address || location.name,
          'form.addressDetail': location.name || '',
          'form.longitude': location.longitude,
          'form.latitude': location.latitude,
          selectedAddress: null,
          'form.addressId': null
        });

        wx.showToast({
          title: '位置选择成功',
          icon: 'success'
        });
      }
    } catch (error) {
      wx.hideLoading();
      if (error.errMsg !== 'chooseLocation:fail cancel') {
        console.error('选择位置失败:', error);
        wx.showToast({
          title: '选择位置失败',
          icon: 'none'
        });
      }
    }
  },

  /**
   * 提交修改
   */
  async submitModify() {
    const { form, orderId, userInfo } = this.data;
    
    // 验证必填字段
    if (!form.address.trim()) {
      wx.showToast({
        title: '请输入服务地址',
        icon: 'none'
      });
      return;
    }

    if (!form.addressDetail.trim()) {
      wx.showToast({
        title: '请输入详细地址',
        icon: 'none'
      });
      return;
    }

    try {
      this.setData({ loading: true });
      wx.showLoading({ title: '修改中...' });

      // 准备提交数据
      const submitData = {
        address: form.address.trim(),
        addressDetail: form.addressDetail.trim(),
        longitude: form.longitude,
        latitude: form.latitude,
        addressRemark: form.addressRemark.trim(),
        addressId: form.addressId,
        userType: 'customer'
      };

      // 调用修改服务地址API
      await orderApi.updateServiceAddress(orderId, submitData);

      wx.hideLoading();
      wx.showToast({
        title: '修改成功',
        icon: 'success'
      });

      // 延迟返回上一页，并触发上一页刷新
      setTimeout(() => {
        // 通过事件总线或者页面栈通知上一页刷新
        const pages = getCurrentPages();
        if (pages.length > 1) {
          const prevPage = pages[pages.length - 2];
          if (prevPage && typeof prevPage.refreshOrderData === 'function') {
            prevPage.refreshOrderData();
          }
        }
        wx.navigateBack();
      }, 1500);

    } catch (error) {
      wx.hideLoading();
      console.error('修改服务地址失败:', error);
      wx.showToast({
        title: error.message || '修改失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack();
  }
});
