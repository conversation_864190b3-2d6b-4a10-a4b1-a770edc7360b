/* pages/service/adress/addAdress.wxss */
.containeraddadress {
  background-color: #f5f5f5;
  padding-bottom: 160rpx;
  padding: 0 24rpx;
}
.adresstip{
  text-align: center;
  padding: 48rpx 0;
}
.adressform{
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
  background-color: white;
  border-radius: 24rpx 24rpx 0 0;
  height: calc(100vh - 230rpx);
  overflow-y: auto;
}
.adressform .diygw-form-item{
  align-items: baseline;
}
.adressform .diygw-form-item .input{
  text-align: right;
  justify-content: flex-end;
  align-items: flex-start;
}
.adressform .diygw-form-item textarea{
  height: 2.3em;
}
.diygw-form-item picker::after{
  line-height: 42rpx;
}
.flex1-clz {
  border-top: 2rpx solid #e4e4e4;
  padding: 16rpx;
  border-radius: 24rpx 24rpx 0rpx 0rpx;
  bottom: 0rpx;
  background-color: #ffffff;
  box-shadow: 0rpx 4rpx 12rpx rgba(31, 31, 31, 0.16);
  overflow: visible;
  left: 0rpx;
}

.flex12-clz {
  flex: 1;
}

.btn-clz {
  padding: 20rpx;
  border-radius: 40rpx;
  color: #fff;
  font-size: 28rpx !important;
  background-color: rgba(255, 67, 145, 1);
  overflow: hidden;
  text-align: center;
}

/* 地图选择位置按钮样式 */
.map-location-btn {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  margin: 16rpx 0;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
  width: 100%;
}

.map-location-btn:active {
  background-color: #e9ecef;
  transform: scale(0.98);
}

.location-icon {
  width: 48rpx;
  height: 48rpx;
  background-color: #ff4391;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.location-icon .icon6 {
  color: #fff;
  font-size: 24rpx;
}

.location-content {
  flex: 1;
}

.location-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.location-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.location-arrow {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.location-arrow .icon6 {
  color: #999;
  font-size: 20rpx;
}